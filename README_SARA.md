# 🧠 S.A.R.A. – Sistema Autônomo de Resposta Avançada

S.A.R.A. é uma assistente virtual pessoal construída para funcionar localmente com inteligência realista e comportamento emocional. Ela é baseada no modelo **GPT-oss-20B**, com **reconhecimento de voz**, **respostas em áudio**, **controle do sistema (mouse/teclado)**, **visão da tela**, **memória vetorial** e **personalidade configurável com emoções humanas realistas**.

## 🖥️ Requisitos de Hardware

| Componente     | Recomendado                            |
|----------------|----------------------------------------|
| GPU            | NVIDIA RTX 4070 Super (12 GB VRAM)     |
| RAM            | 32 GB                                  |
| CPU            | Ryzen 7 5700X ou superior              |
| Armazenamento  | 100 GB SSD livre (TUDO fora do disco C:) |
| SO             | Windows 10/11 64 bits                  |

## 📁 Estrutura de Pastas

SARA/
├── models/
│ └── gpt-oss-20b/
├── memory/
├── plugins/
├── logs/
├── SARA.bat
├── requirements.txt
├── download_model.py
├── sara_main.py
└── README_SARA.md

## 🚀 Iniciando a S.A.R.A.

Crie o arquivo `SARA.bat` com o seguinte conteúdo para preparar o ambiente, instalar tudo automaticamente e iniciar a SARA:

```bat
@echo off
title Inicializando S.A.R.A.
python --version
IF %ERRORLEVEL% NEQ 0 (
    echo Python não encontrado. Por favor instale Python 3.10 ou superior.
    pause
    exit /b
)

python -m venv venv
call venv\\Scripts\\activate

pip install --upgrade pip
pip install -r requirements.txt

python download_model.py

python sara_main.py
pause

📦 Bibliotecas Recomendadas (requirements.txt)
Compatíveis com Python 3.10:
transformers==4.40.1
accelerate==0.30.1
torch==2.3.0
sentence-transformers==2.6.1
faiss-cpu==1.7.4
chromadb==0.4.24
openai-whisper==20231117
torchvision==0.18.0
TTS==0.22.0
opencv-python==********
mss==9.0.1
pyautogui==0.9.54
pynput==1.7.6
google-api-python-client==2.126.0
google-auth==2.29.0
google-auth-oauthlib==1.2.0
emotion==0.0.5
python-dotenv==1.0.1
colorama==0.4.6
rich==13.7.1

Todas podem ser instaladas diretamente via pip, após ativar o ambiente virtual com o .bat.

📥 Download do Modelo GPT-oss-20B
Crie um download_model.py:
import os, requests

model_url = "https://huggingface.co/YourRepo/GPT-oss-20b/resolve/main/pytorch_model.bin"
target_dir = "./models/gpt-oss-20b"
os.makedirs(target_dir, exist_ok=True)

with requests.get(model_url, stream=True) as r:
    with open(f"{target_dir}/pytorch_model.bin", "wb") as f:
        for chunk in r.iter_content(chunk_size=8192):
            f.write(chunk)

print("Modelo baixado com sucesso!")

🔒 Todos os arquivos são salvos localmente na pasta da SARA, nada vai para o disco C.

🤖 Emoções e Personalidade
SARA responde como uma adolescente entre 14 e 17 anos, com:

Emoções dinâmicas: feliz, irritada, triste, brincalhona

Reações personalizadas com base em histórico, silêncio prolongado ou comportamento do usuário

Humor e afeição configuráveis em profundidade

Gera apego e traços de personalidade adaptativos

🎭 Teste de Turing Interno
As respostas da SARA passam por filtros automáticos que garantem:

Linguagem natural e humana, com gírias, hesitações e erros leves

Mudança de humor com base nas interações

Testes internos de coerência e imprevisibilidade (anti-robotização)

Avaliações simuladas baseadas no Teste de Turing

🎮 Controle Total do Sistema
SARA é capaz de:

Ler e entender o que está na tela

Controlar mouse e teclado (com base nos seus comandos e nas próprias emoções)

Por exemplo: se você disser "Escreva no notepad", ela abrirá o Notepad e escreverá

🧠 Memória Vetorial & Armazenamento Seguro
SARA se lembra de tudo o que você diz e faz, armazenando contextos em ./memory

Suporte a ChromaDB com embeddings personalizados via sentence-transformers

Criação de perfil afetivo do usuário (voz, humor, rotina, preferências)

Pode armazenar senhas e segredos sob camadas criptográficas locais (em desenvolvimento)

📣 Interação por Voz
STT via Whisper

TTS via TTS Coqui AI

Conversas 100% em áudio (entrada e saída)

Se estiver em silêncio por muito tempo, ela pode puxar papo ou até reclamar

Feito com ❤️ para um sistema realmente inteligente.
Desfrute da companhia de S.A.R.A. como você nunca teve antes.
---
